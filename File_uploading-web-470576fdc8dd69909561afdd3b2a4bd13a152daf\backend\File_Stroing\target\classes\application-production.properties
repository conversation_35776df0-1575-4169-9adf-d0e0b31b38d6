# Production Database Configuration - H2 (No External DB Needed)
spring.datasource.url=jdbc:h2:mem:filehub;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=false

# Server Configuration
server.port=${PORT:8080}

# File Upload Configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# CORS Configuration
cors.allowed-origins=${FRONTEND_URL:https://filehuub.netlify.app,http://localhost:5173}

# Logging Configuration
logging.level.com.example.filestoring=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN

# JPA Configuration
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
