{"name": "filestoringfrontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.17", "axios": "^1.8.4", "browser-image-compression": "^2.0.2", "framer-motion": "^12.6.2", "jodit-react": "^5.2.18", "js-cookie": "^3.0.5", "lucide-react": "^0.501.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-router-dom": "^7.4.1", "tailwindcss": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}